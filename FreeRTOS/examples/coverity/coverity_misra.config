{"version": "2.0", "standard": "c2012", "title": "Coverity MISRA Configuration", "deviations": [{"deviation": "Rule 3.1", "reason": "We post HTTP links in code comments which contain // inside comments blocks."}, {"deviation": "Rule 14.4", "reason": "do while( 0 ) pattern is used in macros to prevent extra semi-colon."}, {"deviation": "Directive 4.4", "reason": "Code snippet is used in comment to help explanation."}, {"deviation": "Directive 4.5", "reason": "Allow names that MISRA considers ambiguous."}, {"deviation": "Directive 4.6", "reason": "Allow port to use primitive type with typedefs."}, {"deviation": "Directive 4.8", "reason": "HeapRegion_t and HeapStats_t are used only in heap files but declared in portable.h which is included in multiple source files. As a result, these definitions appear in multiple source files where they are not used."}, {"deviation": "Directive 4.9", "reason": "FreeRTOS-Kernel is optimised to work on small micro-controllers. To achieve that, function-like macros are used."}, {"deviation": "Rule 2.3", "reason": "FreeRTOS defines types which is used in application."}, {"deviation": "Rule 2.4", "reason": "Allow to define unused tag."}, {"deviation": "Rule 2.5", "reason": "Allow to define unused macro."}, {"deviation": "Rule 5.9", "reason": "Allow to define identifier with the same name in structure and global variable."}, {"deviation": "Rule 8.7", "reason": "API functions are not used by the library outside of the files they are defined; however, they must be externally visible in order to be used by an application."}, {"deviation": "Rule 8.9", "reason": "Allow to object to be defined in wider scope for debug purpose."}, {"deviation": "Rule 8.13", "reason": "Allow to not to use const-qualified type for callback function."}, {"deviation": "Rule 11.4", "reason": "Allow to convert between a pointer to object and an interger type for stack alignment."}, {"deviation": "Rule 15.4", "reason": "Allow to use multiple break statements in a loop."}, {"deviation": "Rule 15.5", "reason": "Allow to use multiple points of exit."}, {"deviation": "Rule 17.8", "reason": "Allow to update the parameters of a function."}, {"deviation": "Rule 18.4", "reason": "Allow to use pointer arithmetic."}, {"deviation": "Rule 19.2", "reason": "Allow to use union."}, {"deviation": "Rule 20.5", "reason": "Allow to use #undef for MPU wrappers."}]}