# Configuration support for FreeRTOS

## Overview

Every FreeRTOS project requires FreeRTOSConfig.h located in their include path.  In this folder you will find a sample FreeRTOSConfig.h that will assist you in preparing the configuration for your application.

The FreeRTOSConfig.h in this folder is used in the minimal_freertos_example project provided and it not guaranteed to have the same configuration between updates.