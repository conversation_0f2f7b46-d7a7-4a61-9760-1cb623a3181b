/**
 * @file lv_example_grad.h
 *
 */

#ifndef LV_EXAMPLE_GRAD_H
#define LV_EXAMPLE_GRAD_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_grad_1(void);
void lv_example_grad_2(void);
void lv_example_grad_3(void);
void lv_example_grad_4(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_GRAD_H*/
