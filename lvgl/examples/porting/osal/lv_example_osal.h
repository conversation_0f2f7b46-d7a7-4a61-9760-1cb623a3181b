/**
 * @file lv_example_osal.h
 *
 */

#ifndef LV_EXAMPLE_OSAL_H
#define LV_EXAMPLE_OSAL_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_osal(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*LV_EXAMPLE_OSAL_H*/
