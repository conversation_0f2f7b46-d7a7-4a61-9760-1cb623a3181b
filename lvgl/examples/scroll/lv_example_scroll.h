/**
 * @file lv_example_scroll.h
 *
 */

#ifndef LV_EXAMPLE_SCROLL_H
#define LV_EXAMPLE_SCROLL_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_scroll_1(void);
void lv_example_scroll_2(void);
void lv_example_scroll_3(void);
void lv_example_scroll_4(void);
void lv_example_scroll_5(void);
void lv_example_scroll_6(void);
void lv_example_scroll_7(void);
void lv_example_scroll_8(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_SCROLL_H*/
